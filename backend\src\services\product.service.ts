import { PrismaClient } from '../generated/client';
import { <PERSON>rror<PERSON>and<PERSON> } from '../middlewares/error.middleware';
import {
  ProductCreateInput,
  ProductUpdateInput,
  ProductFilters,
  ProductSortOptions,
  ProductPaginationOptions,
  ProductServiceOptions,
  ProductResponse,
  ProductListResponse,
} from '../types/product.types';

export class ProductService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Tüm ürünleri getir (filtreleme, sayfalama, sıralama ile)
   */
  async getAllProducts(
    companyId: string,
    options: ProductServiceOptions = {}
  ): Promise<ProductListResponse> {
    try {
      const {
        filters = {},
        sort = { sortBy: 'displayOrder', sortOrder: 'asc' },
        pagination = { page: 1, limit: 20 },
        includeRelations = true,
      } = options;

      // Where koşulları
      const where: any = {
        companyId,
        active: true, // Varsay<PERSON>lan olarak sadece aktif <PERSON>
      };

      // Filtreleme
      if (filters.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { code: { contains: filters.search, mode: 'insensitive' } },
          { barcode: { contains: filters.search, mode: 'insensitive' } },
        ];
      }

      if (filters.categoryId) {
        where.categoryId = filters.categoryId;
      }

      if (filters.available !== undefined) {
        where.available = filters.available;
      }

      if (filters.sellable !== undefined) {
        where.sellable = filters.sellable;
      }

      if (filters.active !== undefined) {
        where.active = filters.active;
      }

      if (filters.trackStock !== undefined) {
        where.trackStock = filters.trackStock;
      }

      if (filters.unit) {
        where.unit = filters.unit;
      }

      // Sıralama
      const orderBy: any = {};
      orderBy[sort.sortBy] = sort.sortOrder;

      // Sayfalama hesaplamaları
      const skip = (pagination.page - 1) * pagination.limit;

      // Include relations
      const include = includeRelations
        ? {
            category: {
              select: {
                id: true,
                name: true,
              },
            },
            tax: {
              select: {
                id: true,
                name: true,
                rate: true,
              },
            },
          }
        : undefined;

      // Toplam sayı
      const total = await this.prisma.product.count({ where });

      // Ürünleri getir
      const products = await this.prisma.product.findMany({
        where,
        include,
        orderBy,
        skip,
        take: pagination.limit,
      });

      // Sayfalama bilgileri
      const totalPages = Math.ceil(total / pagination.limit);
      const hasNext = pagination.page < totalPages;
      const hasPrev = pagination.page > 1;

      return {
        products: products as ProductResponse[],
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages,
          hasNext,
          hasPrev,
        },
        filters,
      };
    } catch (error) {
      console.error('ProductService.getAllProducts error:', error);
      throw ErrorHandler.createError('Ürünler getirilemedi', 500, 'PRODUCTS_FETCH_ERROR');
    }
  }

  /**
   * ID ile ürün getir
   */
  async getProductById(id: string, companyId: string): Promise<ProductResponse> {
    try {
      const product = await this.prisma.product.findFirst({
        where: {
          id,
          companyId,
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          tax: {
            select: {
              id: true,
              name: true,
              rate: true,
            },
          },
        },
      });

      if (!product) {
        throw ErrorHandler.createError('Ürün bulunamadı', 404, 'PRODUCT_NOT_FOUND');
      }

      return product as ProductResponse;
    } catch (error) {
      if (error instanceof Error && error.message.includes('PRODUCT_NOT_FOUND')) {
        throw error;
      }
      console.error('ProductService.getProductById error:', error);
      throw ErrorHandler.createError('Ürün getirilemedi', 500, 'PRODUCT_FETCH_ERROR');
    }
  }

  /**
   * Yeni ürün oluştur
   */
  async createProduct(data: ProductCreateInput, companyId: string): Promise<ProductResponse> {
    try {
      // Ürün kodu benzersizlik kontrolü
      const existingProduct = await this.prisma.product.findFirst({
        where: {
          companyId,
          code: data.code,
        },
      });

      if (existingProduct) {
        throw ErrorHandler.createError('Bu ürün kodu zaten kullanılıyor', 400, 'PRODUCT_CODE_EXISTS');
      }

      // Kategori kontrolü
      const category = await this.prisma.category.findFirst({
        where: {
          id: data.categoryId,
          companyId,
        },
      });

      if (!category) {
        throw ErrorHandler.createError('Geçersiz kategori', 400, 'INVALID_CATEGORY');
      }

      // Vergi kontrolü
      const tax = await this.prisma.tax.findFirst({
        where: {
          id: data.taxId,
          companyId,
        },
      });

      if (!tax) {
        throw ErrorHandler.createError('Geçersiz vergi', 400, 'INVALID_TAX');
      }

      const product = await this.prisma.product.create({
        data: {
          ...data,
          companyId,
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          tax: {
            select: {
              id: true,
              name: true,
              rate: true,
            },
          },
        },
      });

      return product as ProductResponse;
    } catch (error) {
      if (error instanceof Error && (
        error.message.includes('PRODUCT_CODE_EXISTS') ||
        error.message.includes('INVALID_CATEGORY') ||
        error.message.includes('INVALID_TAX')
      )) {
        throw error;
      }
      console.error('ProductService.createProduct error:', error);
      throw ErrorHandler.createError('Ürün oluşturulamadı', 500, 'PRODUCT_CREATE_ERROR');
    }
  }

  /**
   * Ürün güncelle
   */
  async updateProduct(id: string, data: ProductUpdateInput, companyId: string): Promise<ProductResponse> {
    try {
      // Ürün varlık kontrolü
      const existingProduct = await this.prisma.product.findFirst({
        where: {
          id,
          companyId,
        },
      });

      if (!existingProduct) {
        throw ErrorHandler.createError('Ürün bulunamadı', 404, 'PRODUCT_NOT_FOUND');
      }

      // Ürün kodu benzersizlik kontrolü (eğer kod değiştiriliyorsa)
      if (data.code && data.code !== existingProduct.code) {
        const codeExists = await this.prisma.product.findFirst({
          where: {
            companyId,
            code: data.code,
            id: { not: id },
          },
        });

        if (codeExists) {
          throw ErrorHandler.createError('Bu ürün kodu zaten kullanılıyor', 400, 'PRODUCT_CODE_EXISTS');
        }
      }

      // Kategori kontrolü (eğer kategori değiştiriliyorsa)
      if (data.categoryId) {
        const category = await this.prisma.category.findFirst({
          where: {
            id: data.categoryId,
            companyId,
          },
        });

        if (!category) {
          throw ErrorHandler.createError('Geçersiz kategori', 400, 'INVALID_CATEGORY');
        }
      }

      // Vergi kontrolü (eğer vergi değiştiriliyorsa)
      if (data.taxId) {
        const tax = await this.prisma.tax.findFirst({
          where: {
            id: data.taxId,
            companyId,
          },
        });

        if (!tax) {
          throw ErrorHandler.createError('Geçersiz vergi', 400, 'INVALID_TAX');
        }
      }

      const product = await this.prisma.product.update({
        where: { id },
        data,
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          tax: {
            select: {
              id: true,
              name: true,
              rate: true,
            },
          },
        },
      });

      return product as ProductResponse;
    } catch (error) {
      if (error instanceof Error && (
        error.message.includes('PRODUCT_NOT_FOUND') ||
        error.message.includes('PRODUCT_CODE_EXISTS') ||
        error.message.includes('INVALID_CATEGORY') ||
        error.message.includes('INVALID_TAX')
      )) {
        throw error;
      }
      console.error('ProductService.updateProduct error:', error);
      throw ErrorHandler.createError('Ürün güncellenemedi', 500, 'PRODUCT_UPDATE_ERROR');
    }
  }

  /**
   * Ürün sil
   */
  async deleteProduct(id: string, companyId: string): Promise<void> {
    try {
      const product = await this.prisma.product.findFirst({
        where: {
          id,
          companyId,
        },
      });

      if (!product) {
        throw ErrorHandler.createError('Ürün bulunamadı', 404, 'PRODUCT_NOT_FOUND');
      }

      // Soft delete (active = false)
      await this.prisma.product.update({
        where: { id },
        data: { active: false },
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes('PRODUCT_NOT_FOUND')) {
        throw error;
      }
      console.error('ProductService.deleteProduct error:', error);
      throw ErrorHandler.createError('Ürün silinemedi', 500, 'PRODUCT_DELETE_ERROR');
    }
  }
}
