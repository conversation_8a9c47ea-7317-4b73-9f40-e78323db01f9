import { z } from 'zod';
import { ProductUnit } from '../generated/client';

// ==================== ENUMS ====================

export const ProductUnitEnum = z.nativeEnum(ProductUnit);

// ==================== BASE SCHEMAS ====================

export const ProductCreateSchema = z.object({
  categoryId: z.string().min(1, 'Kategori ID gereklidir'),
  code: z.string().min(1, 'Ürün kodu gereklidir').max(50, 'Ürün kodu en fazla 50 karakter olabilir'),
  barcode: z.string().optional(),
  name: z.string().min(1, 'Ürün adı gereklidir').max(200, 'Ürün adı en fazla 200 karakter olabilir'),
  description: z.string().optional(),
  image: z.string().optional(),
  basePrice: z.number().positive('Fiyat pozitif olmalıdır'),
  taxId: z.string().min(1, 'Vergi ID gereklidir'),
  trackStock: z.boolean().default(false),
  unit: ProductUnitEnum.default(ProductUnit.PIECE),
  criticalStock: z.number().optional(),
  available: z.boolean().default(true),
  sellable: z.boolean().default(true),
  preparationTime: z.number().int().positive().optional(),
  hasVariants: z.boolean().default(false),
  hasModifiers: z.boolean().default(false),
  displayOrder: z.number().int().default(0),
  active: z.boolean().default(true),
});

export const ProductUpdateSchema = z.object({
  categoryId: z.string().min(1, 'Kategori ID gereklidir').optional(),
  code: z.string().min(1, 'Ürün kodu gereklidir').max(50, 'Ürün kodu en fazla 50 karakter olabilir').optional(),
  barcode: z.string().optional(),
  name: z.string().min(1, 'Ürün adı gereklidir').max(200, 'Ürün adı en fazla 200 karakter olabilir').optional(),
  description: z.string().optional(),
  image: z.string().optional(),
  basePrice: z.number().positive('Fiyat pozitif olmalıdır').optional(),
  taxId: z.string().min(1, 'Vergi ID gereklidir').optional(),
  trackStock: z.boolean().optional(),
  unit: ProductUnitEnum.optional(),
  criticalStock: z.number().optional(),
  available: z.boolean().optional(),
  sellable: z.boolean().optional(),
  preparationTime: z.number().int().positive().optional(),
  hasVariants: z.boolean().optional(),
  hasModifiers: z.boolean().optional(),
  displayOrder: z.number().int().optional(),
  active: z.boolean().optional(),
});

// ==================== QUERY SCHEMAS ====================

export const ProductQuerySchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().int().positive()).default('1'),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().int().positive().max(100)).default('20'),
  search: z.string().optional(),
  categoryId: z.string().optional(),
  available: z.string().transform(val => val === 'true').pipe(z.boolean()).optional(),
  sellable: z.string().transform(val => val === 'true').pipe(z.boolean()).optional(),
  active: z.string().transform(val => val === 'true').pipe(z.boolean()).optional(),
  trackStock: z.string().transform(val => val === 'true').pipe(z.boolean()).optional(),
  unit: ProductUnitEnum.optional(),
  sortBy: z.enum(['name', 'code', 'basePrice', 'createdAt', 'displayOrder']).default('displayOrder'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

export const ProductParamsSchema = z.object({
  id: z.string().min(1, 'Ürün ID gereklidir'),
});

// ==================== RESPONSE SCHEMAS ====================

export const ProductResponseSchema = z.object({
  id: z.string(),
  companyId: z.string(),
  categoryId: z.string(),
  code: z.string(),
  barcode: z.string().nullable(),
  name: z.string(),
  description: z.string().nullable(),
  image: z.string().nullable(),
  basePrice: z.number(),
  taxId: z.string(),
  trackStock: z.boolean(),
  unit: ProductUnitEnum,
  criticalStock: z.number().nullable(),
  available: z.boolean(),
  sellable: z.boolean(),
  preparationTime: z.number().nullable(),
  hasVariants: z.boolean(),
  hasModifiers: z.boolean(),
  displayOrder: z.number(),
  active: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
  syncId: z.string().nullable(),
  lastSyncAt: z.date().nullable(),
  // Relations
  category: z.object({
    id: z.string(),
    name: z.string(),
  }).optional(),
  tax: z.object({
    id: z.string(),
    name: z.string(),
    rate: z.number(),
  }).optional(),
});

export const ProductListResponseSchema = z.object({
  products: z.array(ProductResponseSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  }),
  filters: z.object({
    search: z.string().optional(),
    categoryId: z.string().optional(),
    available: z.boolean().optional(),
    sellable: z.boolean().optional(),
    active: z.boolean().optional(),
    trackStock: z.boolean().optional(),
    unit: ProductUnitEnum.optional(),
  }),
});

// ==================== TYPE EXPORTS ====================

export type ProductCreateInput = z.infer<typeof ProductCreateSchema>;
export type ProductUpdateInput = z.infer<typeof ProductUpdateSchema>;
export type ProductQueryInput = z.infer<typeof ProductQuerySchema>;
export type ProductParamsInput = z.infer<typeof ProductParamsSchema>;
export type ProductResponse = z.infer<typeof ProductResponseSchema>;
export type ProductListResponse = z.infer<typeof ProductListResponseSchema>;

// ==================== SERVICE TYPES ====================

export interface ProductFilters {
  search?: string;
  categoryId?: string;
  available?: boolean;
  sellable?: boolean;
  active?: boolean;
  trackStock?: boolean;
  unit?: ProductUnit;
}

export interface ProductSortOptions {
  sortBy: 'name' | 'code' | 'basePrice' | 'createdAt' | 'displayOrder';
  sortOrder: 'asc' | 'desc';
}

export interface ProductPaginationOptions {
  page: number;
  limit: number;
}

export interface ProductServiceOptions {
  filters?: ProductFilters;
  sort?: ProductSortOptions;
  pagination?: ProductPaginationOptions;
  includeRelations?: boolean;
}
