// Product store - Zustand ile ürün state yönetimi
import { create } from 'zustand';
import { Product, ProductFilters, ProductState, Category, Tax } from '../types/product.types';

interface ProductStore extends ProductState {
  // Categories ve Taxes
  categories: Category[];
  taxes: Tax[];
  
  // Actions
  setProducts: (products: Product[]) => void;
  addProduct: (product: Product) => void;
  updateProduct: (product: Product) => void;
  removeProduct: (productId: string) => void;
  setSelectedProduct: (product: Product | null) => void;
  
  // Loading states
  setLoading: (loading: boolean) => void;
  setCreating: (creating: boolean) => void;
  setUpdating: (updating: boolean) => void;
  setDeleting: (deleting: boolean) => void;
  
  // Error handling
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Filters
  setFilters: (filters: Partial<ProductFilters>) => void;
  clearFilters: () => void;
  
  // Pagination
  setPagination: (pagination: Partial<ProductState['pagination']>) => void;
  
  // Modal states
  openCreateModal: () => void;
  closeCreateModal: () => void;
  openEditModal: (product: Product) => void;
  closeEditModal: () => void;
  openDeleteModal: (product: Product) => void;
  closeDeleteModal: () => void;
  
  // Categories ve Taxes
  setCategories: (categories: Category[]) => void;
  setTaxes: (taxes: Tax[]) => void;
  
  // Reset
  reset: () => void;
}

const initialState: ProductState = {
  products: [],
  selectedProduct: null,
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
  filters: {},
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  },
  isCreateModalOpen: false,
  isEditModalOpen: false,
  isDeleteModalOpen: false,
};

export const useProductStore = create<ProductStore>((set, get) => ({
  ...initialState,
  categories: [],
  taxes: [],

  // Products
  setProducts: (products) => set({ products }),
  
  addProduct: (product) => set((state) => ({
    products: [product, ...state.products],
  })),
  
  updateProduct: (updatedProduct) => set((state) => ({
    products: state.products.map((product) =>
      product.id === updatedProduct.id ? updatedProduct : product
    ),
    selectedProduct: state.selectedProduct?.id === updatedProduct.id ? updatedProduct : state.selectedProduct,
  })),
  
  removeProduct: (productId) => set((state) => ({
    products: state.products.filter((product) => product.id !== productId),
    selectedProduct: state.selectedProduct?.id === productId ? null : state.selectedProduct,
  })),
  
  setSelectedProduct: (product) => set({ selectedProduct: product }),

  // Loading states
  setLoading: (isLoading) => set({ isLoading }),
  setCreating: (isCreating) => set({ isCreating }),
  setUpdating: (isUpdating) => set({ isUpdating }),
  setDeleting: (isDeleting) => set({ isDeleting }),

  // Error handling
  setError: (error) => set({ error }),
  clearError: () => set({ error: null }),

  // Filters
  setFilters: (newFilters) => set((state) => ({
    filters: { ...state.filters, ...newFilters },
    pagination: { ...state.pagination, page: 1 }, // Reset to first page when filtering
  })),
  
  clearFilters: () => set((state) => ({
    filters: {},
    pagination: { ...state.pagination, page: 1 },
  })),

  // Pagination
  setPagination: (newPagination) => set((state) => ({
    pagination: { ...state.pagination, ...newPagination },
  })),

  // Modal states
  openCreateModal: () => set({ 
    isCreateModalOpen: true,
    selectedProduct: null,
    error: null,
  }),
  
  closeCreateModal: () => set({ 
    isCreateModalOpen: false,
    selectedProduct: null,
    error: null,
  }),
  
  openEditModal: (product) => set({ 
    isEditModalOpen: true,
    selectedProduct: product,
    error: null,
  }),
  
  closeEditModal: () => set({ 
    isEditModalOpen: false,
    selectedProduct: null,
    error: null,
  }),
  
  openDeleteModal: (product) => set({ 
    isDeleteModalOpen: true,
    selectedProduct: product,
    error: null,
  }),
  
  closeDeleteModal: () => set({ 
    isDeleteModalOpen: false,
    selectedProduct: null,
    error: null,
  }),

  // Categories ve Taxes
  setCategories: (categories) => set({ categories }),
  setTaxes: (taxes) => set({ taxes }),

  // Reset
  reset: () => set(initialState),
}));

// Selectors for better performance
export const useProductList = () => useProductStore((state) => state.products);
export const useSelectedProduct = () => useProductStore((state) => state.selectedProduct);
export const useProductLoading = () => useProductStore((state) => ({
  isLoading: state.isLoading,
  isCreating: state.isCreating,
  isUpdating: state.isUpdating,
  isDeleting: state.isDeleting,
}));
export const useProductError = () => useProductStore((state) => state.error);
export const useProductFilters = () => useProductStore((state) => state.filters);
export const useProductPagination = () => useProductStore((state) => state.pagination);
export const useProductModals = () => useProductStore((state) => ({
  isCreateModalOpen: state.isCreateModalOpen,
  isEditModalOpen: state.isEditModalOpen,
  isDeleteModalOpen: state.isDeleteModalOpen,
}));
export const useProductCategories = () => useProductStore((state) => state.categories);
export const useProductTaxes = () => useProductStore((state) => state.taxes);
