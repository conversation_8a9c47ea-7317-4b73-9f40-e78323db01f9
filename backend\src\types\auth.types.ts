// Auth ile ilgili TypeScript tipleri
import { UserRole } from '../generated/client';

export interface PinLoginRequest {
  employeeId: string;
  pin: string;
}

export interface PinLoginResponse {
  token: string;
  employee: EmployeeInfo;
  session: SessionInfo;
}

export interface EmployeeInfo {
  id: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  branchId: string | null;
  email: string | null;
  phone: string | null;
}

export interface SessionInfo {
  id: string;
  startedAt: Date;
  branchId: string;
}

export interface JwtPayload {
  userId: string;
  sessionId: string;
  branchId: string;
  companyId: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

export interface ActiveEmployee {
  id: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  branchId: string | null;
  profileImage?: string | null;
}

export interface AuthError {
  code: string;
  message: string;
  statusCode: number;
}

// Rate limiting için
export interface RateLimitInfo {
  attempts: number;
  lastAttempt: Date;
  blockedUntil?: Date;
}
