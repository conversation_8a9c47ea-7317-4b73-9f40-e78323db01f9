// ProductFilters component - Ürün filtreleme bileşeni
import React, { useState } from 'react';
import { ProductFilters as ProductFiltersType, ProductQueryParams, PRODUCT_UNITS, SORT_OPTIONS } from '../../types/product.types';

interface ProductFiltersProps {
  filters: ProductFiltersType;
  categories: Array<{ id: string; name: string }>;
  onFiltersChange: (filters: Partial<ProductQueryParams>) => void;
  onClearFilters: () => void;
}

export const ProductFilters: React.FC<ProductFiltersProps> = ({
  filters,
  categories,
  onFiltersChange,
  onClearFilters,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFiltersChange({ search: e.target.value });
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFiltersChange({ categoryId: e.target.value || undefined });
  };

  const handleUnitChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFiltersChange({ unit: e.target.value as any || undefined });
  };

  const handleAvailableChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    onFiltersChange({ 
      available: value === '' ? undefined : value === 'true' 
    });
  };

  const handleSellableChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    onFiltersChange({ 
      sellable: value === '' ? undefined : value === 'true' 
    });
  };

  const handleActiveChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    onFiltersChange({ 
      active: value === '' ? undefined : value === 'true' 
    });
  };

  const handleTrackStockChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    onFiltersChange({ 
      trackStock: value === '' ? undefined : value === 'true' 
    });
  };

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const [sortBy, sortOrder] = e.target.value.split('-');
    onFiltersChange({
      sortBy: sortBy as 'name' | 'code' | 'basePrice' | 'createdAt' | 'displayOrder',
      sortOrder: sortOrder as 'asc' | 'desc'
    });
  };

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== null && value !== ''
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      {/* Ana Arama ve Hızlı Filtreler */}
      <div className="p-4">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Arama */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Ürün adı, kodu veya barkod ile arama yapın..."
                value={filters.search || ''}
                onChange={handleSearchChange}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Kategori Filtresi */}
          <div className="w-full lg:w-48">
            <select
              value={filters.categoryId || ''}
              onChange={handleCategoryChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Tüm Kategoriler</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* Sıralama */}
          <div className="w-full lg:w-48">
            <select
              onChange={handleSortChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              {SORT_OPTIONS.map((option) => (
                <React.Fragment key={option.value}>
                  <option value={`${option.value}-asc`}>
                    {option.label} (A-Z)
                  </option>
                  <option value={`${option.value}-desc`}>
                    {option.label} (Z-A)
                  </option>
                </React.Fragment>
              ))}
            </select>
          </div>

          {/* Gelişmiş Filtreler Toggle */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg 
              className={`w-4 h-4 mr-2 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
            Gelişmiş Filtreler
            {hasActiveFilters && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Aktif
              </span>
            )}
          </button>
        </div>
      </div>

      {/* Gelişmiş Filtreler */}
      {isExpanded && (
        <div className="border-t border-gray-200 p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Birim */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Birim
              </label>
              <select
                value={filters.unit || ''}
                onChange={handleUnitChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Tüm Birimler</option>
                {PRODUCT_UNITS.map((unit) => (
                  <option key={unit.value} value={unit.value}>
                    {unit.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Mevcut Durumu */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mevcut Durumu
              </label>
              <select
                value={filters.available === undefined ? '' : filters.available.toString()}
                onChange={handleAvailableChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Tümü</option>
                <option value="true">Mevcut</option>
                <option value="false">Mevcut Değil</option>
              </select>
            </div>

            {/* Satış Durumu */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Satış Durumu
              </label>
              <select
                value={filters.sellable === undefined ? '' : filters.sellable.toString()}
                onChange={handleSellableChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Tümü</option>
                <option value="true">Satışta</option>
                <option value="false">Satışta Değil</option>
              </select>
            </div>

            {/* Aktif Durumu */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Aktif Durumu
              </label>
              <select
                value={filters.active === undefined ? '' : filters.active.toString()}
                onChange={handleActiveChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Tümü</option>
                <option value="true">Aktif</option>
                <option value="false">Pasif</option>
              </select>
            </div>

            {/* Stok Takibi */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Stok Takibi
              </label>
              <select
                value={filters.trackStock === undefined ? '' : filters.trackStock.toString()}
                onChange={handleTrackStockChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Tümü</option>
                <option value="true">Stok Takipli</option>
                <option value="false">Stok Takipsiz</option>
              </select>
            </div>
          </div>

          {/* Filtreleri Temizle */}
          {hasActiveFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <button
                onClick={onClearFilters}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Filtreleri Temizle
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
