import { Request, Response, NextFunction } from 'express';
import { ProductService } from '../services/product.service';
import {
  ProductCreateSchema,
  ProductUpdateSchema,
  ProductQuerySchema,
  ProductParamsSchema,
} from '../types/product.types';
import { <PERSON>rrorHandler } from '../middlewares/error.middleware';

export class ProductController {
  private productService: ProductService;

  constructor() {
    this.productService = new ProductService();
  }

  /**
   * Tüm ürünleri getir
   * GET /api/v1/products
   */
  getAllProducts = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user?.companyId) {
        throw ErrorHandler.createError('Şirket bilgisi gereklidir', 400, 'COMPANY_REQUIRED');
      }

      // Query parametrelerini validate et
      const queryResult = ProductQuerySchema.safeParse(req.query);
      if (!queryResult.success) {
        throw ErrorHandler.createError(
          'Geçersiz query parametreleri',
          400,
          'INVALID_QUERY_PARAMS',
          queryResult.error.errors
        );
      }

      const {
        page,
        limit,
        search,
        categoryId,
        available,
        sellable,
        active,
        trackStock,
        unit,
        sortBy,
        sortOrder,
      } = queryResult.data;

      const options = {
        filters: {
          search,
          categoryId,
          available,
          sellable,
          active,
          trackStock,
          unit,
        },
        sort: {
          sortBy,
          sortOrder,
        },
        pagination: {
          page,
          limit,
        },
        includeRelations: true,
      };

      const result = await this.productService.getAllProducts(req.user.companyId, options);

      res.status(200).json({
        success: true,
        message: 'Ürünler başarıyla getirildi',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * ID ile ürün getir
   * GET /api/v1/products/:id
   */
  getProductById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user?.companyId) {
        throw ErrorHandler.createError('Şirket bilgisi gereklidir', 400, 'COMPANY_REQUIRED');
      }

      // Params validate et
      const paramsResult = ProductParamsSchema.safeParse(req.params);
      if (!paramsResult.success) {
        throw ErrorHandler.createError(
          'Geçersiz ürün ID',
          400,
          'INVALID_PRODUCT_ID',
          paramsResult.error.errors
        );
      }

      const { id } = paramsResult.data;
      const product = await this.productService.getProductById(id, req.user.companyId);

      res.status(200).json({
        success: true,
        message: 'Ürün başarıyla getirildi',
        data: product,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Yeni ürün oluştur
   * POST /api/v1/products
   */
  createProduct = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user?.companyId) {
        throw ErrorHandler.createError('Şirket bilgisi gereklidir', 400, 'COMPANY_REQUIRED');
      }

      // Body validate et
      const bodyResult = ProductCreateSchema.safeParse(req.body);
      if (!bodyResult.success) {
        throw ErrorHandler.createError(
          'Geçersiz ürün bilgileri',
          400,
          'INVALID_PRODUCT_DATA',
          bodyResult.error.errors
        );
      }

      const product = await this.productService.createProduct(bodyResult.data, req.user.companyId);

      res.status(201).json({
        success: true,
        message: 'Ürün başarıyla oluşturuldu',
        data: product,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Ürün güncelle
   * PUT /api/v1/products/:id
   */
  updateProduct = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user?.companyId) {
        throw ErrorHandler.createError('Şirket bilgisi gereklidir', 400, 'COMPANY_REQUIRED');
      }

      // Params validate et
      const paramsResult = ProductParamsSchema.safeParse(req.params);
      if (!paramsResult.success) {
        throw ErrorHandler.createError(
          'Geçersiz ürün ID',
          400,
          'INVALID_PRODUCT_ID',
          paramsResult.error.errors
        );
      }

      // Body validate et
      const bodyResult = ProductUpdateSchema.safeParse(req.body);
      if (!bodyResult.success) {
        throw ErrorHandler.createError(
          'Geçersiz ürün bilgileri',
          400,
          'INVALID_PRODUCT_DATA',
          bodyResult.error.errors
        );
      }

      const { id } = paramsResult.data;
      const product = await this.productService.updateProduct(id, bodyResult.data, req.user.companyId);

      res.status(200).json({
        success: true,
        message: 'Ürün başarıyla güncellendi',
        data: product,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Ürün sil
   * DELETE /api/v1/products/:id
   */
  deleteProduct = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user?.companyId) {
        throw ErrorHandler.createError('Şirket bilgisi gereklidir', 400, 'COMPANY_REQUIRED');
      }

      // Params validate et
      const paramsResult = ProductParamsSchema.safeParse(req.params);
      if (!paramsResult.success) {
        throw ErrorHandler.createError(
          'Geçersiz ürün ID',
          400,
          'INVALID_PRODUCT_ID',
          paramsResult.error.errors
        );
      }

      const { id } = paramsResult.data;
      await this.productService.deleteProduct(id, req.user.companyId);

      res.status(200).json({
        success: true,
        message: 'Ürün başarıyla silindi',
        data: null,
      });
    } catch (error) {
      next(error);
    }
  };
}
