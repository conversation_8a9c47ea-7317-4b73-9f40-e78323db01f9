// Product ile ilgili TypeScript tipleri
export interface Product {
  id: string;
  companyId: string;
  categoryId: string;
  code: string;
  barcode?: string | null;
  name: string;
  description?: string | null;
  image?: string | null;
  basePrice: number;
  taxId: string;
  trackStock: boolean;
  unit: ProductUnit;
  criticalStock?: number | null;
  available: boolean;
  sellable: boolean;
  preparationTime?: number | null;
  hasVariants: boolean;
  hasModifiers: boolean;
  displayOrder: number;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  syncId?: string | null;
  lastSyncAt?: string | null;
  // Relations
  category?: {
    id: string;
    name: string;
  };
  tax?: {
    id: string;
    name: string;
    rate: number;
  };
}

export type ProductUnit = 'PIECE' | 'KG' | 'GRAM' | 'LITER' | 'ML' | 'PORTION';

export interface ProductCreateRequest {
  categoryId: string;
  code: string;
  barcode?: string;
  name: string;
  description?: string;
  image?: string;
  basePrice: number;
  taxId: string;
  trackStock?: boolean;
  unit?: ProductUnit;
  criticalStock?: number;
  available?: boolean;
  sellable?: boolean;
  preparationTime?: number;
  hasVariants?: boolean;
  hasModifiers?: boolean;
  displayOrder?: number;
  active?: boolean;
}

export interface ProductUpdateRequest {
  categoryId?: string;
  code?: string;
  barcode?: string;
  name?: string;
  description?: string;
  image?: string;
  basePrice?: number;
  taxId?: string;
  trackStock?: boolean;
  unit?: ProductUnit;
  criticalStock?: number;
  available?: boolean;
  sellable?: boolean;
  preparationTime?: number;
  hasVariants?: boolean;
  hasModifiers?: boolean;
  displayOrder?: number;
  active?: boolean;
}

export interface ProductFilters {
  search?: string;
  categoryId?: string;
  available?: boolean;
  sellable?: boolean;
  active?: boolean;
  trackStock?: boolean;
  unit?: ProductUnit;
}

export interface ProductQueryParams extends ProductFilters {
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'code' | 'basePrice' | 'createdAt' | 'displayOrder';
  sortOrder?: 'asc' | 'desc';
}

export interface ProductListResponse {
  products: Product[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: ProductFilters;
}

export interface ProductResponse {
  success: boolean;
  message: string;
  data: Product;
}

export interface ProductListApiResponse {
  success: boolean;
  message: string;
  data: ProductListResponse;
}

// Form validation için
export interface ProductFormData {
  categoryId: string;
  code: string;
  barcode: string;
  name: string;
  description: string;
  basePrice: string; // Form'da string olarak tutulur
  taxId: string;
  trackStock: boolean;
  unit: ProductUnit;
  criticalStock: string; // Form'da string olarak tutulur
  available: boolean;
  sellable: boolean;
  preparationTime: string; // Form'da string olarak tutulur
  displayOrder: string; // Form'da string olarak tutulur
}

// UI State için
export interface ProductState {
  products: Product[];
  selectedProduct: Product | null;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  filters: ProductFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  // Modal states
  isCreateModalOpen: boolean;
  isEditModalOpen: boolean;
  isDeleteModalOpen: boolean;
}

// Category ve Tax için basit tipler
export interface Category {
  id: string;
  name: string;
}

export interface Tax {
  id: string;
  name: string;
  code: string;
  rate: number;
}

// Unit options için
export const PRODUCT_UNITS: { value: ProductUnit; label: string }[] = [
  { value: 'PIECE', label: 'Adet' },
  { value: 'KG', label: 'Kilogram' },
  { value: 'GRAM', label: 'Gram' },
  { value: 'LITER', label: 'Litre' },
  { value: 'ML', label: 'Mililitre' },
  { value: 'PORTION', label: 'Porsiyon' },
];

// Sort options için
export const SORT_OPTIONS: { value: string; label: string }[] = [
  { value: 'displayOrder', label: 'Sıralama' },
  { value: 'name', label: 'Ürün Adı' },
  { value: 'code', label: 'Ürün Kodu' },
  { value: 'basePrice', label: 'Fiyat' },
  { value: 'createdAt', label: 'Oluşturma Tarihi' },
];
