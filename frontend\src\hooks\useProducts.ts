// useProducts hook - Product API çağrıları ve state management
import { useCallback, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { apiService } from '../services/api.service';
import { useProductStore } from '../store/productStore';
import { ProductCreateRequest, ProductUpdateRequest, ProductQueryParams } from '../types/product.types';

export const useProducts = () => {
  const {
    products,
    selectedProduct,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    error,
    filters,
    pagination,
    categories,
    taxes,
    isCreateModalOpen,
    isEditModalOpen,
    isDeleteModalOpen,
    
    // Actions
    setProducts,
    addProduct,
    updateProduct: updateProductInStore,
    removeProduct,
    setSelectedProduct,
    setLoading,
    setCreating,
    setUpdating,
    setDeleting,
    setError,
    clearError,
    setFilters,
    clearFilters,
    setPagination,
    setCategories,
    setTaxes,
    
    // Modals
    openCreateModal,
    closeCreateModal,
    openEditModal,
    closeEditModal,
    openDeleteModal,
    closeDeleteModal,
  } = useProductStore();

  // Ürünleri getir
  const fetchProducts = useCallback(async (params?: ProductQueryParams) => {
    try {
      setLoading(true);
      clearError();
      
      const queryParams = {
        ...filters,
        page: pagination.page,
        limit: pagination.limit,
        ...params,
      };

      const response = await apiService.getProducts(queryParams);
      
      setProducts(response.products);
      setPagination({
        page: response.pagination.page,
        limit: response.pagination.limit,
        total: response.pagination.total,
        totalPages: response.pagination.totalPages,
        hasNext: response.pagination.hasNext,
        hasPrev: response.pagination.hasPrev,
      });
      
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Ürünler yüklenirken hata oluştu';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.page, pagination.limit, setLoading, clearError, setProducts, setPagination, setError]);

  // Kategorileri getir
  const fetchCategories = useCallback(async () => {
    try {
      const categoriesData = await apiService.getCategories();
      setCategories(categoriesData);
    } catch (error: any) {
      console.error('Kategoriler yüklenirken hata:', error);
      toast.error('Kategoriler yüklenirken hata oluştu');
    }
  }, [setCategories]);

  // Vergi oranlarını getir
  const fetchTaxes = useCallback(async () => {
    try {
      const taxesData = await apiService.getTaxes();
      setTaxes(taxesData);
    } catch (error: any) {
      console.error('Vergi oranları yüklenirken hata:', error);
      toast.error('Vergi oranları yüklenirken hata oluştu');
    }
  }, [setTaxes]);

  // Yeni ürün oluştur
  const createProduct = useCallback(async (productData: ProductCreateRequest) => {
    try {
      setCreating(true);
      clearError();
      
      const newProduct = await apiService.createProduct(productData);
      addProduct(newProduct);
      closeCreateModal();
      
      toast.success('Ürün başarıyla oluşturuldu');
      
      // Listeyi yenile
      await fetchProducts();
      
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Ürün oluşturulurken hata oluştu';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setCreating(false);
    }
  }, [setCreating, clearError, addProduct, closeCreateModal, fetchProducts, setError]);

  // Ürün güncelle
  const updateProduct = useCallback(async (id: string, productData: ProductUpdateRequest) => {
    try {
      setUpdating(true);
      clearError();
      
      const updatedProduct = await apiService.updateProduct(id, productData);
      updateProductInStore(updatedProduct);
      closeEditModal();
      
      toast.success('Ürün başarıyla güncellendi');
      
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Ürün güncellenirken hata oluştu';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setUpdating(false);
    }
  }, [setUpdating, clearError, updateProductInStore, closeEditModal, setError]);

  // Ürün sil
  const deleteProduct = useCallback(async (id: string) => {
    try {
      setDeleting(true);
      clearError();
      
      await apiService.deleteProduct(id);
      removeProduct(id);
      closeDeleteModal();
      
      toast.success('Ürün başarıyla silindi');
      
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Ürün silinirken hata oluştu';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setDeleting(false);
    }
  }, [setDeleting, clearError, removeProduct, closeDeleteModal, setError]);

  // Filtreleme
  const applyFilters = useCallback((newFilters: Partial<ProductQueryParams>) => {
    setFilters(newFilters);
    // Filtreleme sonrası otomatik olarak fetchProducts çağrılacak (useEffect ile)
  }, [setFilters]);

  // Sayfa değiştir
  const changePage = useCallback((page: number) => {
    setPagination({ page });
    // Sayfa değişimi sonrası otomatik olarak fetchProducts çağrılacak (useEffect ile)
  }, [setPagination]);

  // Sayfa boyutu değiştir
  const changePageSize = useCallback((limit: number) => {
    setPagination({ page: 1, limit });
    // Sayfa boyutu değişimi sonrası otomatik olarak fetchProducts çağrılacak (useEffect ile)
  }, [setPagination]);

  // Yenile
  const refresh = useCallback(() => {
    fetchProducts();
  }, [fetchProducts]);

  // Modal işlemleri
  const handleCreateProduct = useCallback(() => {
    openCreateModal();
  }, [openCreateModal]);

  const handleEditProduct = useCallback((product: any) => {
    openEditModal(product);
  }, [openEditModal]);

  const handleDeleteProduct = useCallback((product: any) => {
    openDeleteModal(product);
  }, [openDeleteModal]);

  // İlk yükleme
  useEffect(() => {
    fetchProducts();
    fetchCategories();
    fetchTaxes();
  }, []);

  // Filters veya pagination değiştiğinde yeniden yükle
  useEffect(() => {
    fetchProducts();
  }, [filters, pagination.page, pagination.limit]);

  return {
    // Data
    products,
    selectedProduct,
    categories,
    taxes,
    
    // Loading states
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    
    // Error
    error,
    clearError,
    
    // Filters & Pagination
    filters,
    pagination,
    applyFilters,
    clearFilters,
    changePage,
    changePageSize,
    
    // Actions
    createProduct,
    updateProduct,
    deleteProduct,
    refresh,
    
    // Modal states
    isCreateModalOpen,
    isEditModalOpen,
    isDeleteModalOpen,
    
    // Modal actions
    handleCreateProduct,
    handleEditProduct,
    handleDeleteProduct,
    closeCreateModal,
    closeEditModal,
    closeDeleteModal,
    
    // Utils
    setSelectedProduct,
  };
};
