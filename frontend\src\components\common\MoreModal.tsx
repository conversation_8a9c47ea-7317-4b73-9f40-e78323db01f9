import React from 'react';
import { useNavigate } from 'react-router-dom';

interface MoreModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const MoreModal: React.FC<MoreModalProps> = ({ isOpen, onClose }) => {
  const navigate = useNavigate();

  const handleProductManagement = () => {
    navigate('/products');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl w-full max-w-4xl mx-4 max-h-[80vh] overflow-y-auto shadow-lg">
        {/* Header - Sticky */}
        <div className="sticky top-0 bg-white z-10 flex items-center justify-between p-6 border-b border-gray-100 rounded-t-2xl">
          <h2 className="text-2xl font-semibold text-[#202325]"><PERSON><PERSON></h2>
          <button
            onClick={onClose}
            className="bg-[#F5F5F5] rounded-full w-10 h-10 flex items-center justify-center hover:bg-gray-200 transition-colors"
          >
            <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Tools Section */}
          <div className="mb-8">
            <h3 className="text-sm font-semibold text-[#636566] mb-4">Araçlar</h3>
            <div className="flex gap-4 flex-wrap">
              {/* Ürün Yönetimi */}
              <button
                onClick={handleProductManagement}
                className="bg-white border border-[#F5F5F5] rounded-2xl w-[200px] p-6 hover:shadow-md transition-shadow shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)]"
              >
                <div className="flex flex-col items-start gap-3">
                  <div className="w-[51px] h-[51px] bg-[#F0F8FF] rounded-2xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#025CCA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 5l7 7-7 7" />
                    </svg>
                  </div>
                  <span className="text-base font-medium text-[#202325] text-left">Ürün Yönetimi</span>
                </div>
              </button>

              <button className="bg-white border border-[#F5F5F5] rounded-2xl w-[200px] p-6 hover:shadow-md transition-shadow shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)]">
                <div className="flex flex-col items-start gap-3">
                  <div className="w-[51px] h-[51px] bg-[#F0F8FF] rounded-2xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#025CCA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <span className="text-base font-medium text-[#202325] text-left">Outlet Bilgileri</span>
                </div>
              </button>

              <button className="bg-white border border-[#F5F5F5] rounded-2xl w-[200px] p-6 hover:shadow-md transition-shadow shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)]">
                <div className="flex flex-col items-start gap-3">
                  <div className="w-[51px] h-[51px] bg-[#F0F8FF] rounded-2xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#025CCA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <span className="text-base font-medium text-[#202325] text-left">Rapor Özeti</span>
                </div>
              </button>

              <button className="bg-white border border-[#F5F5F5] rounded-2xl w-[200px] p-6 hover:shadow-md transition-shadow shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)]">
                <div className="flex flex-col items-start gap-3">
                  <div className="w-[51px] h-[51px] bg-[#F0F8FF] rounded-2xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#025CCA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                  </div>
                  <span className="text-base font-medium text-[#202325] text-left">Müşteri Verileri</span>
                </div>
              </button>
            </div>
          </div>

          {/* Settings Section */}
          <div className="mb-8">
            <h3 className="text-sm font-semibold text-[#636566] mb-4">Ayarlar</h3>
            <div className="flex gap-4">
              <button className="bg-white border border-[#F5F5F5] rounded-2xl w-[200px] p-6 hover:shadow-md transition-shadow shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)]">
                <div className="flex flex-col items-start gap-3">
                  <div className="w-[51px] h-[51px] bg-[#F0F8FF] rounded-2xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#025CCA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                    </svg>
                  </div>
                  <span className="text-base font-medium text-[#202325] text-left">Bağlı Cihazlar</span>
                </div>
              </button>

              <button className="bg-white border border-[#F5F5F5] rounded-2xl w-[200px] p-6 hover:shadow-md transition-shadow shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)]">
                <div className="flex flex-col items-start gap-3">
                  <div className="w-[51px] h-[51px] bg-[#F0F8FF] rounded-2xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#025CCA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <span className="text-base font-medium text-[#202325] text-left">Bildirimler</span>
                </div>
              </button>

              <button className="bg-white border border-[#F5F5F5] rounded-2xl w-[200px] p-6 hover:shadow-md transition-shadow shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)]">
                <div className="flex flex-col items-start gap-3">
                  <div className="w-[51px] h-[51px] bg-[#F0F8FF] rounded-2xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#025CCA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </div>
                  <span className="text-base font-medium text-[#202325] text-left">Görünüm</span>
                </div>
              </button>

              <button className="bg-white border border-[#F5F5F5] rounded-2xl w-[200px] p-6 hover:shadow-md transition-shadow shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)]">
                <div className="flex flex-col items-start gap-3">
                  <div className="w-[51px] h-[51px] bg-[#F0F8FF] rounded-2xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#025CCA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                    </svg>
                  </div>
                  <span className="text-base font-medium text-[#202325] text-left">Dil</span>
                </div>
              </button>
            </div>
          </div>

          {/* Help Section */}
          <div>
            <h3 className="text-sm font-semibold text-[#636566] mb-4">Yardım</h3>
            <div className="flex gap-4">
              <button className="bg-white border border-[#F5F5F5] rounded-2xl w-[200px] p-6 hover:shadow-md transition-shadow shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)]">
                <div className="flex flex-col items-start gap-3">
                  <div className="w-[51px] h-[51px] bg-[#F0F8FF] rounded-2xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#025CCA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <span className="text-base font-medium text-[#202325] text-left">Yardım Merkezi</span>
                </div>
              </button>

              <button className="bg-white border border-[#F5F5F5] rounded-2xl w-[200px] p-6 hover:shadow-md transition-shadow shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)]">
                <div className="flex flex-col items-start gap-3">
                  <div className="w-[51px] h-[51px] bg-[#F0F8FF] rounded-2xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#025CCA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <span className="text-base font-medium text-[#202325] text-left">Gizlilik Politikası</span>
                </div>
              </button>

              <button className="bg-white border border-[#F5F5F5] rounded-2xl w-[200px] p-6 hover:shadow-md transition-shadow shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)]">
                <div className="flex flex-col items-start gap-3">
                  <div className="w-[51px] h-[51px] bg-[#F0F8FF] rounded-2xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#025CCA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <span className="text-base font-medium text-[#202325] text-left">Uygulama Bilgileri</span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MoreModal;
