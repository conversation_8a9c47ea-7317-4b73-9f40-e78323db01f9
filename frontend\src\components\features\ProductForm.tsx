// ProductForm component - <PERSON><PERSON><PERSON>n ekleme/düzenleme formu
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Product, PRODUCT_UNITS } from '../../types/product.types';

// Zod validation schema
const productSchema = z.object({
  categoryId: z.string().min(1, 'Kategori seçiniz'),
  code: z.string().min(1, 'Ürün kodu gereklidir').max(50, 'Ürün kodu en fazla 50 karakter olabilir'),
  barcode: z.string(),
  name: z.string().min(1, 'Ürün adı gereklidir').max(200, 'Ürün adı en fazla 200 karakter olabilir'),
  description: z.string(),
  basePrice: z.string().min(1, 'Fiyat gereklidir').refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, 'Geçerli bir fiyat giriniz'),
  taxId: z.string().min(1, 'Vergi oranı seçiniz'),
  trackStock: z.boolean(),
  unit: z.enum(['PIECE', 'KG', 'GRAM', 'LITER', 'ML', 'PORTION']),
  criticalStock: z.string().refine((val) => {
    if (!val || val === '') return true;
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0;
  }, 'Geçerli bir stok miktarı giriniz'),
  available: z.boolean(),
  sellable: z.boolean(),
  preparationTime: z.string().refine((val) => {
    if (!val || val === '') return true;
    const num = parseInt(val);
    return !isNaN(num) && num > 0;
  }, 'Geçerli bir hazırlama süresi giriniz'),
  displayOrder: z.string().refine((val) => {
    if (!val || val === '') return true;
    const num = parseInt(val);
    return !isNaN(num) && num >= 0;
  }, 'Geçerli bir sıralama değeri giriniz'),
});

type ProductFormSchema = z.infer<typeof productSchema>;

interface ProductFormProps {
  product?: Product | null;
  categories: Array<{ id: string; name: string }>;
  taxes: Array<{ id: string; name: string; rate: number }>;
  isLoading: boolean;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

export const ProductForm: React.FC<ProductFormProps> = ({
  product,
  categories,
  taxes,
  isLoading,
  onSubmit,
  onCancel,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<ProductFormSchema>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      categoryId: '',
      code: '',
      barcode: '',
      name: '',
      description: '',
      basePrice: '',
      taxId: '',
      trackStock: false,
      unit: 'PIECE',
      criticalStock: '',
      available: true,
      sellable: true,
      preparationTime: '',
      displayOrder: '0',
    },
  });

  const trackStock = watch('trackStock');

  // Product değiştiğinde formu güncelle
  useEffect(() => {
    if (product) {
      reset({
        categoryId: product.categoryId,
        code: product.code,
        barcode: product.barcode || '',
        name: product.name,
        description: product.description || '',
        basePrice: product.basePrice.toString(),
        taxId: product.taxId,
        trackStock: product.trackStock,
        unit: product.unit,
        criticalStock: product.criticalStock?.toString() || '',
        available: product.available,
        sellable: product.sellable,
        preparationTime: product.preparationTime?.toString() || '',
        displayOrder: product.displayOrder.toString(),
      });
    } else {
      reset({
        categoryId: '',
        code: '',
        barcode: '',
        name: '',
        description: '',
        basePrice: '',
        taxId: '',
        trackStock: false,
        unit: 'PIECE',
        criticalStock: '',
        available: true,
        sellable: true,
        preparationTime: '',
        displayOrder: '0',
      });
    }
  }, [product, reset]);

  const handleFormSubmit = (data: ProductFormSchema) => {
    // Form verilerini API formatına dönüştür
    const submitData = {
      categoryId: data.categoryId,
      code: data.code,
      barcode: data.barcode || undefined,
      name: data.name,
      description: data.description || undefined,
      basePrice: parseFloat(data.basePrice),
      taxId: data.taxId,
      trackStock: data.trackStock,
      unit: data.unit,
      criticalStock: data.criticalStock ? parseFloat(data.criticalStock) : undefined,
      available: data.available,
      sellable: data.sellable,
      preparationTime: data.preparationTime ? parseInt(data.preparationTime) : undefined,
      displayOrder: data.displayOrder ? parseInt(data.displayOrder) : 0,
    };

    onSubmit(submitData);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Temel Bilgiler */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Ürün Adı */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Ürün Adı *
          </label>
          <input
            type="text"
            {...register('name')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-[#F5F5F5] focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Ürün adını giriniz"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
          )}
        </div>

        {/* Ürün Kodu */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Ürün Kodu *
          </label>
          <input
            type="text"
            {...register('code')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-[#F5F5F5] focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Ürün kodunu giriniz"
          />
          {errors.code && (
            <p className="mt-1 text-sm text-red-600">{errors.code.message}</p>
          )}
        </div>

        {/* Barkod */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Barkod
          </label>
          <input
            type="text"
            {...register('barcode')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-[#F5F5F5] focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Barkod giriniz"
          />
          {errors.barcode && (
            <p className="mt-1 text-sm text-red-600">{errors.barcode.message}</p>
          )}
        </div>

        {/* Kategori */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Kategori *
          </label>
          <select
            {...register('categoryId')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-[#F5F5F5] focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Kategori seçiniz</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
          {errors.categoryId && (
            <p className="mt-1 text-sm text-red-600">{errors.categoryId.message}</p>
          )}
        </div>

        {/* Fiyat */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Fiyat (TL) *
          </label>
          <input
            type="number"
            step="0.01"
            min="0"
            {...register('basePrice')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-[#F5F5F5] focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="0.00"
          />
          {errors.basePrice && (
            <p className="mt-1 text-sm text-red-600">{errors.basePrice.message}</p>
          )}
        </div>

        {/* Vergi */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Vergi Oranı *
          </label>
          <select
            {...register('taxId')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-[#F5F5F5] focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Vergi oranı seçiniz</option>
            {taxes.map((tax) => (
              <option key={tax.id} value={tax.id}>
                {tax.name} (%{tax.rate})
              </option>
            ))}
          </select>
          {errors.taxId && (
            <p className="mt-1 text-sm text-red-600">{errors.taxId.message}</p>
          )}
        </div>

        {/* Birim */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Birim
          </label>
          <select
            {...register('unit')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            {PRODUCT_UNITS.map((unit) => (
              <option key={unit.value} value={unit.value}>
                {unit.label}
              </option>
            ))}
          </select>
        </div>

        {/* Hazırlama Süresi */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Hazırlama Süresi (dakika)
          </label>
          <input
            type="number"
            min="0"
            {...register('preparationTime')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="0"
          />
          {errors.preparationTime && (
            <p className="mt-1 text-sm text-red-600">{errors.preparationTime.message}</p>
          )}
        </div>
      </div>

      {/* Açıklama */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Açıklama
        </label>
        <textarea
          {...register('description')}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          placeholder="Ürün açıklaması giriniz"
        />
        {errors.description && (
          <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
        )}
      </div>

      {/* Stok Takibi */}
      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            {...register('trackStock')}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label className="ml-2 block text-sm text-gray-900">
            Stok takibi yap
          </label>
        </div>

        {/* Kritik Stok - Sadece stok takibi açıksa göster */}
        {trackStock && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Kritik Stok Miktarı
            </label>
            <input
              type="number"
              step="0.001"
              min="0"
              {...register('criticalStock')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="0"
            />
            {errors.criticalStock && (
              <p className="mt-1 text-sm text-red-600">{errors.criticalStock.message}</p>
            )}
          </div>
        )}
      </div>

      {/* Durum Seçenekleri */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            {...register('available')}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label className="ml-2 block text-sm text-gray-900">
            Mevcut
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            {...register('sellable')}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label className="ml-2 block text-sm text-gray-900">
            Satışta
          </label>
        </div>
      </div>

      {/* Sıralama */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Sıralama
        </label>
        <input
          type="number"
          min="0"
          {...register('displayOrder')}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          placeholder="0"
        />
        {errors.displayOrder && (
          <p className="mt-1 text-sm text-red-600">{errors.displayOrder.message}</p>
        )}
      </div>

      {/* Butonlar */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          İptal
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          {isLoading && (
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          )}
          {product ? 'Güncelle' : 'Oluştur'}
        </button>
      </div>
    </form>
  );
};
