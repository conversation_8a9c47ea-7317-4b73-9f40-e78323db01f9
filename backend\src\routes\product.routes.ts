import { Router } from 'express';
import { ProductController } from '../controllers/product.controller';
import { AuthMiddleware } from '../middlewares/auth.middleware';

const router = Router();
const productController = new ProductController();

// ==================== MIDDLEWARE ====================
// Tüm product routes'ları için authentication gerekli
router.use(AuthMiddleware.authenticate);

// ==================== ROUTES ====================

/**
 * @route   GET /api/v1/products
 * @desc    Tüm ürünleri getir (filtreleme, sayfalama, sıralama ile)
 * @access  Private (Authentication required)
 * @query   {
 *   page?: number,
 *   limit?: number,
 *   search?: string,
 *   categoryId?: string,
 *   available?: boolean,
 *   sellable?: boolean,
 *   active?: boolean,
 *   trackStock?: boolean,
 *   unit?: ProductUnit,
 *   sortBy?: 'name' | 'code' | 'basePrice' | 'createdAt' | 'displayOrder',
 *   sortOrder?: 'asc' | 'desc'
 * }
 */
router.get('/', productController.getAllProducts);

/**
 * @route   GET /api/v1/products/:id
 * @desc    ID ile ürün getir
 * @access  Private (Authentication required)
 * @params  { id: string }
 */
router.get('/:id', productController.getProductById);

/**
 * @route   POST /api/v1/products
 * @desc    Yeni ürün oluştur
 * @access  Private (Authentication required)
 * @body    {
 *   categoryId: string,
 *   code: string,
 *   barcode?: string,
 *   name: string,
 *   description?: string,
 *   image?: string,
 *   basePrice: number,
 *   taxId: string,
 *   trackStock?: boolean,
 *   unit?: ProductUnit,
 *   criticalStock?: number,
 *   available?: boolean,
 *   sellable?: boolean,
 *   preparationTime?: number,
 *   hasVariants?: boolean,
 *   hasModifiers?: boolean,
 *   displayOrder?: number,
 *   active?: boolean
 * }
 */
router.post('/', productController.createProduct);

/**
 * @route   PUT /api/v1/products/:id
 * @desc    Ürün güncelle
 * @access  Private (Authentication required)
 * @params  { id: string }
 * @body    {
 *   categoryId?: string,
 *   code?: string,
 *   barcode?: string,
 *   name?: string,
 *   description?: string,
 *   image?: string,
 *   basePrice?: number,
 *   taxId?: string,
 *   trackStock?: boolean,
 *   unit?: ProductUnit,
 *   criticalStock?: number,
 *   available?: boolean,
 *   sellable?: boolean,
 *   preparationTime?: number,
 *   hasVariants?: boolean,
 *   hasModifiers?: boolean,
 *   displayOrder?: number,
 *   active?: boolean
 * }
 */
router.put('/:id', productController.updateProduct);

/**
 * @route   DELETE /api/v1/products/:id
 * @desc    Ürün sil (soft delete)
 * @access  Private (Authentication required)
 * @params  { id: string }
 */
router.delete('/:id', productController.deleteProduct);

export default router;
