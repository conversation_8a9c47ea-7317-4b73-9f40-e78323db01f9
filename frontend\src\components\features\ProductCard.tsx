// ProductCard component - <PERSON>rün kartı bileşeni
import React from 'react';
import { Product } from '../../types/product.types';

interface ProductCardProps {
  product: Product;
  onEdit: (product: Product) => void;
  onDelete: (product: Product) => void;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product, onEdit, onDelete }) => {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2,
    }).format(price);
  };

  const getUnitLabel = (unit: string) => {
    const unitLabels = {
      PIECE: 'Adet',
      KG: 'Kg',
      GRAM: 'Gr',
      LITER: 'Lt',
      ML: 'Ml',
      PORTION: 'Porsiyon',
    };
    return unitLabels[unit as keyof typeof unitLabels] || unit;
  };

  const getStatusColor = (available: boolean, sellable: boolean, active: boolean) => {
    if (!active) return 'bg-gray-100 text-gray-600';
    if (!available) return 'bg-red-100 text-red-600';
    if (!sellable) return 'bg-yellow-100 text-yellow-600';
    return 'bg-green-100 text-green-600';
  };

  const getStatusText = (available: boolean, sellable: boolean, active: boolean) => {
    if (!active) return 'Pasif';
    if (!available) return 'Mevcut Değil';
    if (!sellable) return 'Satışta Değil';
    return 'Aktif';
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200">
      {/* Ürün Resmi */}
      <div className="aspect-w-16 aspect-h-9 bg-gray-100 rounded-t-lg overflow-hidden">
        {product.image ? (
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-32 object-cover"
          />
        ) : (
          <div className="w-full h-32 flex items-center justify-center bg-gray-50">
            <svg className="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        )}
      </div>

      {/* Ürün Bilgileri */}
      <div className="p-4">
        {/* Başlık ve Durum */}
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-lg font-medium text-gray-900 truncate flex-1 mr-2">
            {product.name}
          </h3>
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(product.available, product.sellable, product.active)}`}>
            {getStatusText(product.available, product.sellable, product.active)}
          </span>
        </div>

        {/* Açıklama */}
        {product.description && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {product.description}
          </p>
        )}

        {/* Ürün Detayları */}
        <div className="space-y-2 mb-4">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">Kod:</span>
            <span className="text-sm font-medium text-gray-900">{product.code}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">Fiyat:</span>
            <span className="text-lg font-bold text-blue-600">{formatPrice(product.basePrice)}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">Birim:</span>
            <span className="text-sm font-medium text-gray-900">{getUnitLabel(product.unit)}</span>
          </div>
          
          {product.category && (
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">Kategori:</span>
              <span className="text-sm font-medium text-gray-900">{product.category.name}</span>
            </div>
          )}
          
          {product.trackStock && product.criticalStock && (
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">Kritik Stok:</span>
              <span className="text-sm font-medium text-orange-600">{product.criticalStock}</span>
            </div>
          )}
        </div>

        {/* Etiketler */}
        <div className="flex flex-wrap gap-1 mb-4">
          {product.trackStock && (
            <span className="px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded">
              Stok Takipli
            </span>
          )}
          {product.hasVariants && (
            <span className="px-2 py-1 text-xs bg-purple-100 text-purple-600 rounded">
              Varyantlı
            </span>
          )}
          {product.hasModifiers && (
            <span className="px-2 py-1 text-xs bg-indigo-100 text-indigo-600 rounded">
              Ekstra Seçenekli
            </span>
          )}
          {product.preparationTime && (
            <span className="px-2 py-1 text-xs bg-orange-100 text-orange-600 rounded">
              {product.preparationTime} dk
            </span>
          )}
        </div>

        {/* Aksiyon Butonları */}
        <div className="flex space-x-2">
          <button
            onClick={() => onEdit(product)}
            className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Düzenle
          </button>
          
          <button
            onClick={() => onDelete(product)}
            className="flex-1 bg-red-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors duration-200 flex items-center justify-center"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Sil
          </button>
        </div>
      </div>
    </div>
  );
};
